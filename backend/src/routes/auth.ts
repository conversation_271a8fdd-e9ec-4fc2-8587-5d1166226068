import express from 'express';
import { login, logout, getSession, devLogin } from '../controllers/authController';
import { authMiddleware } from '../middleware/auth';
import { redirectToLightspeed, handleCallback } from '../controllers/lightspeedAuthController';
import { asyncHandler } from '../utils/asyncHandler';

const router = express.Router();

// Session management
router.get('/session', authMiddleware, asyncHandler(getSession));
router.post('/logout', asyncHandler(logout));

// Development-only authentication (for testing with demo users)
if (process.env.NODE_ENV === 'development') {
  router.post('/dev-login', asyncHandler(devLogin));
}

// Lightspeed OAuth (primary authentication method)
router.get('/start-lightspeed', asyncHandler(redirectToLightspeed));
router.get('/callback', asyncHand<PERSON>(handleCallback));

// Legacy local login endpoint (returns error directing to Lightspeed)
router.post('/login', asyncHandler(login));

export default router; 