services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: suitsync
      POSTGRES_PASSWORD: supersecret
      POSTGRES_DB: suitsync
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "suitsync"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: npm run dev
    volumes:
      - ./backend:/app
      - /app/node_modules
    env_file:
      - .env.docker
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 5s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command: npm run dev
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    env_file:
      - ./frontend/.env.docker
    environment:
      NODE_ENV: development
    ports:
      - "3001:3000"
    depends_on:
      - backend

volumes:
  db_data: