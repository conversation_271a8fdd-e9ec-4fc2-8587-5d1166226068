{"name": "suitsync-frontend", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/axios": "^0.9.36", "@types/qrcode.react": "^1.0.5", "@types/react-big-calendar": "^1.16.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.522.0", "next": "14.2.4", "next-pwa": "^5.6.0", "qrcode.react": "^4.2.0", "react": "18.2.0", "react-big-calendar": "^1.8.6", "react-dom": "18.2.0", "react-hot-toast": "2.5.2", "react-qr-code": "^2.0.16", "react-use-pwa-install": "^1.0.3", "recharts": "2.15.3", "swr": "2.3.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "24.0.1", "autoprefixer": "10.4.21", "eslint": "^9.30.0", "eslint-config-next": "^15.3.4", "framer-motion": "10.12.4", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "postcss": "8.5.5", "tailwindcss": "3.4.17"}}