{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["pages", "components", "styles", "src/pages", "src/components", "next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}